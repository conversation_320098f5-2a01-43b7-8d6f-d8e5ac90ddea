{"name": "Finance Tracker Configuration", "description": "Configuration workflow for Enhanced Finance Tracker system", "version": "2.0.0", "environment": {"development": {"database": {"host": "localhost", "port": 5432, "database": "finance_tracker_dev", "ssl": false}, "ai": {"openai": {"model": "gpt-4", "maxTokens": 4000, "temperature": 0.3}, "claude": {"model": "claude-3-sonnet-20240229", "maxTokens": 4000, "temperature": 0.3}}, "webhooks": {"baseUrl": "http://localhost:5678", "timeout": 30000}}, "production": {"database": {"host": "{{$env.DB_HOST}}", "port": "{{$env.DB_PORT}}", "database": "{{$env.DB_NAME}}", "ssl": true}, "ai": {"openai": {"model": "gpt-4", "maxTokens": 4000, "temperature": 0.2}, "claude": {"model": "claude-3-sonnet-20240229", "maxTokens": 4000, "temperature": 0.2}}, "webhooks": {"baseUrl": "{{$env.N8N_WEBHOOK_BASE_URL}}", "timeout": 60000}}}, "workflows": {"main": {"id": "finance-tracker-main", "webhook": "/webhook/finance-tracker", "description": "Main orchestration workflow"}, "dataCollection": {"manualEntry": {"id": "manual-entry", "webhook": "/webhook/transaction-entry", "description": "Manual transaction entry"}, "fileUpload": {"id": "file-upload", "webhook": "/webhook/file-upload", "description": "CSV/Excel file upload processing"}, "bankApi": {"id": "bank-api", "webhook": "/webhook/bank-sync", "description": "Bank API integration"}, "emailParser": {"id": "email-parser", "webhook": "/webhook/email-parse", "description": "Email transaction parsing"}}, "aiAnalysis": {"spendingAnalyzer": {"id": "spending-analyzer", "webhook": "/webhook/analyze-spending", "description": "AI-powered spending analysis"}, "insightGenerator": {"id": "insight-generator", "webhook": "/webhook/generate-insights", "description": "Financial insights generation"}, "anomalyDetector": {"id": "anomaly-detector", "webhook": "/webhook/detect-anomalies", "description": "Anomaly detection system"}, "forecastingEngine": {"id": "forecasting-engine", "webhook": "/webhook/forecast", "description": "Financial forecasting"}}, "reporting": {"dailySummary": {"id": "daily-summary", "webhook": "/webhook/daily-report", "description": "Daily financial summary"}, "weeklyReport": {"id": "weekly-report", "webhook": "/webhook/weekly-report", "description": "Weekly financial report"}, "monthlyAnalysis": {"id": "monthly-analysis", "webhook": "/webhook/monthly-analysis", "description": "Monthly financial analysis"}, "alertSystem": {"id": "alert-system", "webhook": "/webhook/alerts", "description": "Alert and notification system"}}, "integrations": {"googleSheets": {"id": "google-sheets-sync", "webhook": "/webhook/sheets-sync", "description": "Google Sheets integration"}, "databaseSync": {"id": "database-sync", "webhook": "/webhook/db-sync", "description": "Database synchronization"}, "notificationHub": {"id": "notification-hub", "webhook": "/webhook/notifications", "description": "Multi-channel notifications"}}}, "database": {"tables": {"transactions": {"columns": ["id SERIAL PRIMARY KEY", "user_id VARCHAR(255) NOT NULL", "amount DECIMAL(12,2) NOT NULL", "currency VARCHAR(3) DEFAULT 'USD'", "category VARCHAR(100)", "subcategory VARCHAR(100)", "description TEXT", "merchant VARCHAR(255)", "transaction_date TIMESTAMP NOT NULL", "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP", "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP", "source VARCHAR(50) DEFAULT 'manual'", "external_id VARCHAR(255)", "metadata JSONB", "tags TEXT[]"], "indexes": ["CREATE INDEX idx_transactions_user_date ON transactions(user_id, transaction_date)", "CREATE INDEX idx_transactions_category ON transactions(category)", "CREATE INDEX idx_transactions_amount ON transactions(amount)", "CREATE INDEX idx_transactions_source ON transactions(source)"]}, "categories": {"columns": ["id SERIAL PRIMARY KEY", "name VARCHAR(100) UNIQUE NOT NULL", "parent_id INTEGER REFERENCES categories(id)", "color VARCHAR(7)", "icon VARCHAR(50)", "budget_limit DECIMAL(12,2)", "is_active BOOLEAN DEFAULT true", "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"]}, "budgets": {"columns": ["id SERIAL PRIMARY KEY", "user_id VARCHAR(255) NOT NULL", "category_id INTEGER REFERENCES categories(id)", "amount DECIMAL(12,2) NOT NULL", "period VARCHAR(20) DEFAULT 'monthly'", "start_date DATE NOT NULL", "end_date DATE", "is_active BOOLEAN DEFAULT true", "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"]}, "ai_insights": {"columns": ["id SERIAL PRIMARY KEY", "user_id VARCHAR(255) NOT NULL", "insight_type VARCHAR(50) NOT NULL", "title VARCHAR(255) NOT NULL", "content TEXT NOT NULL", "confidence_score DECIMAL(3,2)", "action_items JSONB", "metadata JSONB", "is_read BOOLEAN DEFAULT false", "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP", "expires_at TIMESTAMP"]}, "finance_tracker_logs": {"columns": ["id SERIAL PRIMARY KEY", "request_id VARCHAR(255) NOT NULL", "timestamp TIMESTAMP NOT NULL", "request_type VARCHAR(50)", "route VARCHAR(50)", "status VARCHAR(20)", "response_data JSONB", "processing_time INTEGER", "error_message TEXT", "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"]}}}, "ai": {"prompts": {"spendingAnalysis": {"system": "You are a financial analyst AI specializing in personal finance. Analyze spending patterns and provide actionable insights.", "template": "Analyze the following transaction data and provide insights on spending patterns, trends, and recommendations:\n\nTransaction Data:\n{transactionData}\n\nUser Context:\n{userContext}\n\nPlease provide:\n1. Spending pattern analysis\n2. Notable trends or changes\n3. Budget recommendations\n4. Areas for optimization\n5. Actionable next steps"}, "anomalyDetection": {"system": "You are an anomaly detection AI for financial transactions. Identify unusual patterns and potential issues.", "template": "Review the following transaction data for anomalies:\n\nRecent Transactions:\n{recentTransactions}\n\nHistorical Patterns:\n{historicalPatterns}\n\nIdentify:\n1. Unusual spending amounts\n2. Unexpected merchants or categories\n3. Timing anomalies\n4. Potential fraud indicators\n5. Recommended actions"}, "insightGeneration": {"system": "You are a financial advisor AI. Generate personalized insights and recommendations based on financial data.", "template": "Based on the financial analysis, generate personalized insights:\n\nFinancial Summary:\n{financialSummary}\n\nGoals and Preferences:\n{userGoals}\n\nProvide:\n1. Key financial insights\n2. Personalized recommendations\n3. Goal progress assessment\n4. Optimization opportunities\n5. Next steps for improvement"}}}, "integrations": {"googleSheets": {"dashboardTemplate": {"sheets": [{"name": "Dashboard", "columns": ["Date", "Amount", "Category", "Description", "Balance"]}, {"name": "Monthly Summary", "columns": ["Month", "Income", "Expenses", "Net", "Savings Rate"]}, {"name": "Category Analysis", "columns": ["Category", "Amount", "Percentage", "Budget", "<PERSON><PERSON><PERSON>"]}]}}, "notifications": {"channels": {"email": {"enabled": true, "templates": ["daily_summary", "weekly_report", "alerts"]}, "slack": {"enabled": true, "templates": ["alerts", "insights"]}, "telegram": {"enabled": false, "templates": ["alerts"]}}}}, "security": {"encryption": {"algorithm": "AES-256-GCM", "keyRotationDays": 90}, "apiRateLimit": {"requests": 1000, "window": "1h"}, "dataRetention": {"transactions": "7 years", "logs": "1 year", "insights": "2 years"}}}