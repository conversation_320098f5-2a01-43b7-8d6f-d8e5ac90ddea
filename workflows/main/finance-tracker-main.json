{"name": "Enhanced Finance Tracker - Main Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "finance-tracker", "options": {}}, "id": "webhook-main", "name": "Main Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "finance-tracker-main"}, {"parameters": {"functionCode": "// Enhanced Finance Tracker - Main Controller\n// This function routes requests to appropriate sub-workflows\n\nconst input = $input.all();\nconst requestData = input[0].json;\n\n// Extract request type and data\nconst requestType = requestData.type || 'unknown';\nconst timestamp = new Date().toISOString();\nconst requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n// Log incoming request\nconsole.log(`[${timestamp}] Finance Tracker Request: ${requestType} (ID: ${requestId})`);\n\n// Route based on request type\nlet routingDecision = {\n  requestId: requestId,\n  timestamp: timestamp,\n  originalRequest: requestData,\n  route: 'unknown'\n};\n\nswitch(requestType) {\n  case 'transaction':\n    routingDecision.route = 'data-collection';\n    routingDecision.subWorkflow = 'manual-entry';\n    routingDecision.priority = 'normal';\n    break;\n    \n  case 'file-upload':\n    routingDecision.route = 'data-collection';\n    routingDecision.subWorkflow = 'file-upload';\n    routingDecision.priority = 'normal';\n    break;\n    \n  case 'analysis-request':\n    routingDecision.route = 'ai-analysis';\n    routingDecision.subWorkflow = 'spending-analyzer';\n    routingDecision.priority = 'high';\n    break;\n    \n  case 'report-request':\n    routingDecision.route = 'reporting';\n    routingDecision.subWorkflow = 'report-generator';\n    routingDecision.priority = 'normal';\n    break;\n    \n  case 'alert-config':\n    routingDecision.route = 'configuration';\n    routingDecision.subWorkflow = 'alert-setup';\n    routingDecision.priority = 'low';\n    break;\n    \n  case 'health-check':\n    routingDecision.route = 'monitoring';\n    routingDecision.subWorkflow = 'health-check';\n    routingDecision.priority = 'low';\n    break;\n    \n  default:\n    routingDecision.route = 'error';\n    routingDecision.error = `Unknown request type: ${requestType}`;\n    routingDecision.priority = 'high';\n}\n\n// Add system context\nroutingDecision.systemContext = {\n  version: '2.0.0',\n  environment: process.env.NODE_ENV || 'development',\n  nodeId: 'main-controller',\n  capabilities: [\n    'transaction-processing',\n    'ai-analysis',\n    'reporting',\n    'integrations',\n    'monitoring'\n  ]\n};\n\nreturn [routingDecision];"}, "id": "main-controller", "name": "Main Controller", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.route}}", "operation": "equal", "value2": "data-collection"}]}}, "id": "route-data-collection", "name": "Route: Data Collection", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.route}}", "operation": "equal", "value2": "ai-analysis"}]}}, "id": "route-ai-analysis", "name": "Route: AI Analysis", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.route}}", "operation": "equal", "value2": "reporting"}]}}, "id": "route-reporting", "name": "Route: Reporting", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.route}}", "operation": "equal", "value2": "error"}]}}, "id": "route-error", "name": "Route: Error", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"url": "={{$env.N8N_WEBHOOK_BASE_URL}}/webhook/data-collection", "sendBody": true, "bodyParameters": {"parameters": [{"name": "requestData", "value": "={{$json}}"}]}, "options": {"timeout": 30000}}, "id": "call-data-collection", "name": "Call Data Collection", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 200]}, {"parameters": {"url": "={{$env.N8N_WEBHOOK_BASE_URL}}/webhook/ai-analysis", "sendBody": true, "bodyParameters": {"parameters": [{"name": "requestData", "value": "={{$json}}"}]}, "options": {"timeout": 60000}}, "id": "call-ai-analysis", "name": "Call AI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"url": "={{$env.N8N_WEBHOOK_BASE_URL}}/webhook/reporting", "sendBody": true, "bodyParameters": {"parameters": [{"name": "requestData", "value": "={{$json}}"}]}, "options": {"timeout": 45000}}, "id": "call-reporting", "name": "Call Reporting", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 400]}, {"parameters": {"functionCode": "// Error Handler for Finance Tracker\nconst errorData = $input.all()[0].json;\n\n// Log error details\nconsole.error(`Finance Tracker Error: ${errorData.error}`);\nconsole.error(`Request ID: ${errorData.requestId}`);\nconsole.error(`Original Request:`, errorData.originalRequest);\n\n// Create error response\nconst errorResponse = {\n  success: false,\n  error: {\n    message: errorData.error || 'Unknown error occurred',\n    requestId: errorData.requestId,\n    timestamp: errorData.timestamp,\n    code: 'FINANCE_TRACKER_ERROR'\n  },\n  suggestions: [\n    'Check the request format and try again',\n    'Verify all required fields are provided',\n    'Contact support if the issue persists'\n  ]\n};\n\n// Send error notification if configured\nif (process.env.ERROR_NOTIFICATION_WEBHOOK) {\n  // This would trigger an error notification workflow\n  errorResponse.notificationSent = true;\n}\n\nreturn [errorResponse];"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"functionCode": "// Response Formatter for Finance Tracker\nconst responses = $input.all();\nlet finalResponse = {\n  success: true,\n  timestamp: new Date().toISOString(),\n  results: []\n};\n\n// Process all responses\nresponses.forEach((response, index) => {\n  const data = response.json;\n  \n  if (data.success === false) {\n    finalResponse.success = false;\n    finalResponse.errors = finalResponse.errors || [];\n    finalResponse.errors.push(data.error);\n  } else {\n    finalResponse.results.push({\n      source: data.source || `workflow_${index}`,\n      data: data.result || data,\n      processingTime: data.processingTime || null\n    });\n  }\n});\n\n// Add summary information\nfinalResponse.summary = {\n  totalResponses: responses.length,\n  successfulResponses: finalResponse.results.length,\n  errorCount: finalResponse.errors ? finalResponse.errors.length : 0,\n  overallSuccess: finalResponse.success\n};\n\n// Add performance metrics\nfinalResponse.performance = {\n  totalProcessingTime: responses.reduce((total, r) => {\n    return total + (r.json.processingTime || 0);\n  }, 0),\n  averageResponseTime: responses.length > 0 ? \n    responses.reduce((total, r) => total + (r.json.processingTime || 0), 0) / responses.length : 0\n};\n\nreturn [finalResponse];"}, "id": "response-formatter", "name": "Response Formatter", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1120, 350]}, {"parameters": {"operation": "insert", "table": "finance_tracker_logs", "columns": "request_id, timestamp, request_type, route, status, response_data, processing_time", "additionalFields": {"mode": "independently"}}, "id": "log-to-database", "name": "Log to Database", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 350], "credentials": {"postgres": {"id": "finance-tracker-db", "name": "Finance Tracker Database"}}}], "connections": {"Main Webhook": {"main": [[{"node": "Main Controller", "type": "main", "index": 0}]]}, "Main Controller": {"main": [[{"node": "Route: Data Collection", "type": "main", "index": 0}, {"node": "Route: AI Analysis", "type": "main", "index": 0}, {"node": "Route: Reporting", "type": "main", "index": 0}, {"node": "Route: Error", "type": "main", "index": 0}]]}, "Route: Data Collection": {"main": [[{"node": "Call Data Collection", "type": "main", "index": 0}]]}, "Route: AI Analysis": {"main": [[{"node": "Call AI Analysis", "type": "main", "index": 0}]]}, "Route: Reporting": {"main": [[{"node": "Call Reporting", "type": "main", "index": 0}]]}, "Route: Error": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Call Data Collection": {"main": [[{"node": "Response Formatter", "type": "main", "index": 0}]]}, "Call AI Analysis": {"main": [[{"node": "Response Formatter", "type": "main", "index": 0}]]}, "Call Reporting": {"main": [[{"node": "Response Formatter", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Response Formatter", "type": "main", "index": 0}]]}, "Response Formatter": {"main": [[{"node": "Log to Database", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Asia/Jakarta", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "finance-tracker-main", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "finance-tracker", "name": "finance-tracker"}]}